@tailwind base;
@tailwind components;
@tailwind utilities;

/* Global styles */
html,
body {
  padding: 0;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen,
    Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif;
}

a {
  color: inherit;
  text-decoration: none;
}

* {
  box-sizing: border-box;
}

/* Custom utility classes */
.rounded-image {
  border-radius: 1rem;
}

.container {
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 768px) {
  .container {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

/* Relume UI specific styles */
.bg-background-primary {
  background-color: #ffffff;
}

.border-border-primary {
  border-color: #e5e7eb;
}

.text-background-primary {
  color: #ffffff;
}
