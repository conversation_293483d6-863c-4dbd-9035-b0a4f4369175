import React from 'react';
import Link from 'next/link';
import { useState, useEffect } from 'react';

// useMediaQuery Hook
export function useMediaQuery(query: string): boolean {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    const media = window.matchMedia(query);
    if (media.matches !== matches) {
      setMatches(media.matches);
    }
    const listener = () => setMatches(media.matches);
    window.addEventListener("resize", listener);
    return () => window.removeEventListener("resize", listener);
  }, [matches, query]);

  return matches;
}

// Button Component
export interface ButtonProps {
  children?: React.ReactNode;
  title?: string;
  variant?: 'primary' | 'secondary' | 'link' | 'outline';
  size?: 'sm' | 'md' | 'lg' | 'link' | 'primary';
  iconRight?: React.ReactNode;
  iconLeft?: React.ReactNode;
  className?: string;
  onClick?: () => void;
  href?: string;
  type?: 'button' | 'submit' | 'reset';
}

export function Button({ 
  children, 
  title, 
  variant = 'primary', 
  size = 'md', 
  iconRight, 
  iconLeft,
  className = '',
  onClick,
  href,
  type = 'button'
}: ButtonProps) {
  const baseClasses = 'inline-flex items-center justify-center font-medium transition-all duration-200 rounded-lg border focus:outline-none focus:ring-2 focus:ring-offset-2';
  
  const variantClasses = {
    primary: 'bg-blue-600 text-white border-blue-600 hover:bg-blue-700 hover:border-blue-700 focus:ring-blue-500',
    secondary: 'bg-gray-100 text-gray-900 border-gray-300 hover:bg-gray-200 hover:border-gray-400 focus:ring-gray-500',
    outline: 'bg-transparent text-blue-600 border-blue-600 hover:bg-blue-50 focus:ring-blue-500',
    link: 'text-blue-600 hover:text-blue-800 bg-transparent border-transparent p-0 underline focus:ring-blue-500'
  };
  
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg',
    link: 'p-0 text-base',
    primary: 'px-6 py-3 text-base'
  };
  
  const classes = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`;
  
  const content = (
    <>
      {iconLeft && <span className="mr-2">{iconLeft}</span>}
      {title || children}
      {iconRight && <span className="ml-2">{iconRight}</span>}
    </>
  );
  
  if (href) {
    return (
      <Link href={href} className={classes}>
        {content}
      </Link>
    );
  }
  
  return (
    <button className={classes} onClick={onClick} type={type}>
      {content}
    </button>
  );
}

// Badge Component
export interface BadgeProps {
  children: React.ReactNode;
  variant?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
  className?: string;
}

export function Badge({ children, variant = 'default', className = '' }: BadgeProps) {
  const variantClasses = {
    default: 'bg-gray-100 text-gray-800',
    primary: 'bg-blue-100 text-blue-800',
    secondary: 'bg-purple-100 text-purple-800',
    success: 'bg-green-100 text-green-800',
    warning: 'bg-yellow-100 text-yellow-800',
    danger: 'bg-red-100 text-red-800'
  };
  
  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${variantClasses[variant]} ${className}`}>
      {children}
    </span>
  );
}

// Input Component
export interface InputProps {
  placeholder?: string;
  type?: string;
  className?: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  label?: string;
  error?: string;
  required?: boolean;
}

export function Input({ 
  placeholder, 
  type = 'text', 
  className = '', 
  value, 
  onChange, 
  label, 
  error,
  required = false 
}: InputProps) {
  return (
    <div className="space-y-1">
      {label && (
        <label className="block text-sm font-medium text-gray-700">
          {label} {required && <span className="text-red-500">*</span>}
        </label>
      )}
      <input
        type={type}
        placeholder={placeholder}
        className={`w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${error ? 'border-red-300' : ''} ${className}`}
        value={value}
        onChange={onChange}
        required={required}
      />
      {error && <p className="text-sm text-red-600">{error}</p>}
    </div>
  );
}

// Card Component
export interface CardProps {
  children: React.ReactNode;
  className?: string;
  hover?: boolean;
}

export function Card({ children, className = '', hover = false }: CardProps) {
  return (
    <div className={`bg-white rounded-lg shadow-md p-6 ${hover ? 'hover:shadow-lg transition-shadow duration-200' : ''} ${className}`}>
      {children}
    </div>
  );
}

// Navigation Component
export function Navbar() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = React.useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = React.useState(false);

  return (
    <nav className="w-full bg-white border-b border-gray-200 px-4 lg:px-8 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">AI</span>
            </div>
            <span className="text-xl font-bold text-gray-900">ProductivityDash</span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-8">
            <Link href="/home-page" className="text-gray-700 hover:text-blue-600 transition-colors">Home</Link>
            <Link href="/features-page" className="text-gray-700 hover:text-blue-600 transition-colors">Features</Link>
            <Link href="/pricing-page" className="text-gray-700 hover:text-blue-600 transition-colors">Pricing</Link>
            
            {/* Resources Dropdown */}
            <div className="relative">
              <button 
                className="flex items-center text-gray-700 hover:text-blue-600 transition-colors"
                onClick={() => setIsDropdownOpen(!isDropdownOpen)}
              >
                Resources 
                <svg className="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              {isDropdownOpen && (
                <div className="absolute top-full left-0 mt-2 w-48 bg-white border border-gray-200 rounded-md shadow-lg z-50">
                  <Link href="/blog-page" className="block px-4 py-2 text-gray-700 hover:bg-gray-100 transition-colors">Blog</Link>
                  <Link href="/portfolio-page" className="block px-4 py-2 text-gray-700 hover:bg-gray-100 transition-colors">Portfolio</Link>
                  <Link href="/about-us-page" className="block px-4 py-2 text-gray-700 hover:bg-gray-100 transition-colors">About</Link>
                  <Link href="/contact-us-page" className="block px-4 py-2 text-gray-700 hover:bg-gray-100 transition-colors">Contact</Link>
                  <Link href="/careers-page" className="block px-4 py-2 text-gray-700 hover:bg-gray-100 transition-colors">Careers</Link>
                </div>
              )}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="hidden lg:flex items-center space-x-4">
            <Button variant="secondary" size="sm">Sign Up</Button>
            <Button size="sm">Get Started</Button>
          </div>

          {/* Mobile Menu Button */}
          <button 
            className="lg:hidden p-2"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          >
            <div className="space-y-1">
              <div className="w-6 h-0.5 bg-gray-600"></div>
              <div className="w-6 h-0.5 bg-gray-600"></div>
              <div className="w-6 h-0.5 bg-gray-600"></div>
            </div>
          </button>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="lg:hidden py-4 border-t border-gray-200">
            <div className="space-y-4">
              <Link href="/home-page" className="block text-gray-700 hover:text-blue-600 transition-colors">Home</Link>
              <Link href="/features-page" className="block text-gray-700 hover:text-blue-600 transition-colors">Features</Link>
              <Link href="/pricing-page" className="block text-gray-700 hover:text-blue-600 transition-colors">Pricing</Link>
              <Link href="/blog-page" className="block text-gray-700 hover:text-blue-600 transition-colors">Blog</Link>
              <Link href="/portfolio-page" className="block text-gray-700 hover:text-blue-600 transition-colors">Portfolio</Link>
              <Link href="/about-us-page" className="block text-gray-700 hover:text-blue-600 transition-colors">About</Link>
              <Link href="/contact-us-page" className="block text-gray-700 hover:text-blue-600 transition-colors">Contact</Link>
              <Link href="/careers-page" className="block text-gray-700 hover:text-blue-600 transition-colors">Careers</Link>
              <div className="flex flex-col space-y-2 pt-4">
                <Button variant="secondary" size="sm" className="w-full">Sign Up</Button>
                <Button size="sm" className="w-full">Get Started</Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
}

// Footer Component
export function Footer() {
  return (
    <footer className="bg-gray-900 text-white py-12">
      <div className="max-w-7xl mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">AI</span>
              </div>
              <h3 className="text-lg font-bold">ProductivityDash</h3>
            </div>
            <p className="text-gray-400">
              Revolutionizing productivity with AI automation for real estate professionals.
            </p>
          </div>
          
          <div>
            <h4 className="font-semibold mb-4">Features</h4>
            <ul className="space-y-2 text-gray-400">
              <li><Link href="/task-management-page" className="hover:text-white transition-colors">Task Management</Link></li>
              <li><Link href="/email-automation-page" className="hover:text-white transition-colors">Email Automation</Link></li>
              <li><Link href="/document-generation-page" className="hover:text-white transition-colors">Document Generation</Link></li>
              <li><Link href="/voice-processing-page" className="hover:text-white transition-colors">Voice Processing</Link></li>
              <li><Link href="/crm-synchronization-page" className="hover:text-white transition-colors">CRM Sync</Link></li>
              <li><Link href="/calendar-management-page" className="hover:text-white transition-colors">Calendar Management</Link></li>
            </ul>
          </div>
          
          <div>
            <h4 className="font-semibold mb-4">Company</h4>
            <ul className="space-y-2 text-gray-400">
              <li><Link href="/about-us-page" className="hover:text-white transition-colors">About Us</Link></li>
              <li><Link href="/careers-page" className="hover:text-white transition-colors">Careers</Link></li>
              <li><Link href="/contact-us-page" className="hover:text-white transition-colors">Contact</Link></li>
              <li><Link href="/blog-page" className="hover:text-white transition-colors">Blog</Link></li>
              <li><Link href="/portfolio-page" className="hover:text-white transition-colors">Portfolio</Link></li>
            </ul>
          </div>
          
          <div>
            <h4 className="font-semibold mb-4">Support</h4>
            <ul className="space-y-2 text-gray-400">
              <li><Link href="/pricing-page" className="hover:text-white transition-colors">Pricing</Link></li>
              <li><Link href="/contact-us-page" className="hover:text-white transition-colors">Support</Link></li>
              <li><a href="#" className="hover:text-white transition-colors">Documentation</a></li>
              <li><a href="#" className="hover:text-white transition-colors">API</a></li>
            </ul>
          </div>
        </div>
        
        <div className="border-t border-gray-700 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-400">&copy; 2025 AI Productivity Dashboard. All rights reserved.</p>
          <div className="flex space-x-6 mt-4 md:mt-0">
            <a href="#" className="text-gray-400 hover:text-white transition-colors">Privacy</a>
            <a href="#" className="text-gray-400 hover:text-white transition-colors">Terms</a>
            <a href="#" className="text-gray-400 hover:text-white transition-colors">Security</a>
          </div>
        </div>
      </div>
    </footer>
  );
}

// Tabs Components
export interface TabsProps {
  children: React.ReactNode;
  defaultValue?: string;
  className?: string;
}

export function Tabs({ children, defaultValue, className = '' }: TabsProps) {
  const [activeTab, setActiveTab] = React.useState(defaultValue || '');
  
  return (
    <div className={`tabs ${className}`} data-active-tab={activeTab}>
      {React.Children.map(children, child => {
        if (React.isValidElement(child)) {
          return React.cloneElement(child, { activeTab, setActiveTab } as any);
        }
        return child;
      })}
    </div>
  );
}

export function TabsList({ children, className = '' }: { children: React.ReactNode; className?: string }) {
  return (
    <div className={`flex space-x-1 border-b border-gray-200 ${className}`}>
      {children}
    </div>
  );
}

export function TabsTrigger({ 
  value, 
  children, 
  className = '',
  activeTab,
  setActiveTab
}: { 
  value: string; 
  children: React.ReactNode; 
  className?: string;
  activeTab?: string;
  setActiveTab?: (value: string) => void;
}) {
  const isActive = activeTab === value;
  
  return (
    <button
      className={`px-4 py-2 text-sm font-medium transition-colors border-b-2 ${
        isActive 
          ? 'text-blue-600 border-blue-600' 
          : 'text-gray-500 border-transparent hover:text-gray-700'
      } ${className}`}
      onClick={() => setActiveTab?.(value)}
    >
      {children}
    </button>
  );
}

export function TabsContent({ 
  value, 
  children, 
  className = '',
  activeTab 
}: { 
  value: string; 
  children: React.ReactNode; 
  className?: string;
  activeTab?: string;
}) {
  if (activeTab !== value) return null;
  
  return (
    <div className={`mt-6 ${className}`}>
      {children}
    </div>
  );
}

// Checkbox Component
export interface CheckboxProps {
  id?: string;
  checked?: boolean;
  onChange?: (checked: boolean) => void;
  className?: string;
  disabled?: boolean;
}

export function Checkbox({ id, checked, onChange, className = '', disabled = false }: CheckboxProps) {
  return (
    <input
      type="checkbox"
      id={id}
      checked={checked}
      onChange={(e) => onChange?.(e.target.checked)}
      disabled={disabled}
      className={`w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 focus:ring-2 ${className}`}
    />
  );
}

// Label Component
export interface LabelProps {
  children: React.ReactNode;
  htmlFor?: string;
  className?: string;
}

export function Label({ children, htmlFor, className = '' }: LabelProps) {
  return (
    <label htmlFor={htmlFor} className={`block text-sm font-medium text-gray-700 ${className}`}>
      {children}
    </label>
  );
}

// Textarea Component
export interface TextareaProps {
  placeholder?: string;
  className?: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  rows?: number;
  cols?: number;
  required?: boolean;
}

export function Textarea({ 
  placeholder, 
  className = '', 
  value, 
  onChange, 
  rows = 4,
  cols,
  required = false 
}: TextareaProps) {
  return (
    <textarea
      placeholder={placeholder}
      className={`w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${className}`}
      value={value}
      onChange={onChange}
      rows={rows}
      cols={cols}
      required={required}
    />
  );
}
