"use client";

import {
  Accordion,
  Accordion<PERSON>ontent,
  AccordionItem,
  AccordionTrigger,
  Button,
} from "../../components/ui";
import React from "react";

export function Faq3() {
  return (
    <section id="relume" className="px-[5%] py-16 md:py-24 lg:py-28">
      <div className="container grid grid-cols-1 gap-y-12 md:grid-cols-2 md:gap-x-12 lg:grid-cols-[.75fr,1fr] lg:gap-x-20">
        <div>
          <h2 className="rb-5 mb-5 text-5xl font-bold md:mb-6 md:text-7xl lg:text-8xl">
            FAQs
          </h2>
          <p className="md:text-md">
            Find answers to common questions about our AI Task Automation
            Assistant and its features.
          </p>
          <div className="mt-6 md:mt-8">
            <Button title="Contact" variant="secondary">
              Contact
            </Button>
          </div>
        </div>
        <Accordion type="multiple">
          <AccordionItem value="item-0">
            <AccordionTrigger className="md:py-5 md:text-md">
              What is the system?
            </AccordionTrigger>
            <AccordionContent className="md:pb-6">
              Our AI Task Automation Assistant is a comprehensive platform
              designed to streamline productivity. It integrates voice capture,
              task management, and email automation into one dashboard. This
              system is specifically tailored for real estate professionals.
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="item-1">
            <AccordionTrigger className="md:py-5 md:text-md">
              How does it work?
            </AccordionTrigger>
            <AccordionContent className="md:pb-6">
              The system utilizes advanced AI technology to automate tasks and
              enhance communication. Users can capture voice notes, manage
              tasks, and automate emails seamlessly. Integration with existing
              tools ensures a smooth workflow.
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="item-2">
            <AccordionTrigger className="md:py-5 md:text-md">
              Is it user-friendly?
            </AccordionTrigger>
            <AccordionContent className="md:pb-6">
              Yes, the interface is designed with user experience in mind. It
              offers intuitive navigation and easy access to features. Users can
              quickly adapt to the system without extensive training.
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="item-3">
            <AccordionTrigger className="md:py-5 md:text-md">
              What support is available?
            </AccordionTrigger>
            <AccordionContent className="md:pb-6">
              We provide comprehensive support through various channels. Users
              can access tutorials, FAQs, and direct customer service. Our team
              is dedicated to ensuring your success with the system.
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="item-4">
            <AccordionTrigger className="md:py-5 md:text-md">
              Can I integrate it?
            </AccordionTrigger>
            <AccordionContent className="md:pb-6">
              Absolutely! The system is designed to integrate with popular tools
              and platforms. This allows for a customized experience that fits
              your business needs. Integration enhances overall productivity and
              efficiency.
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>
    </section>
  );
}
