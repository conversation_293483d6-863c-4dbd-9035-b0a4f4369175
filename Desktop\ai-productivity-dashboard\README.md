# AI Productivity Dashboard

A comprehensive React/Next.js application showcasing AI automation features for real estate professionals.

## Features

- **Task Management** - Streamline your workflow with intelligent task automation
- **Email Automation** - Automate email communications and responses
- **Document Generation** - Generate professional documents automatically
- **Voice Processing** - Convert voice commands to actionable tasks
- **CRM Synchronization** - Seamlessly sync your CRM data
- **Calendar Management** - Efficient calendar and scheduling automation

## Technologies Used

- **Next.js 14** - React framework for production
- **React 18** - UI library
- **TypeScript** - Type-safe JavaScript
- **Tailwind CSS** - Utility-first CSS framework
- **Framer Motion** - Animation library
- **React Icons** - Icon library
- **Relume UI** - Component library

## Getting Started

### Prerequisites

- Node.js 18.17 or later
- npm or yarn package manager

### Installation

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd ai-productivity-dashboard
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Run the development server:
   ```bash
   npm run dev
   ```

4. Open [http://localhost:3000](http://localhost:3000) in your browser to see the application.

### Build for Production

```bash
npm run build
npm start
```

## Project Structure

```
ai-productivity-dashboard/
├── pages/                 # Next.js pages
├── home/                  # Home page components
├── features/              # Features page components
├── task-management/       # Task management components
├── email-automation/      # Email automation components
├── document-generation/   # Document generation components
├── voice-processing/      # Voice processing components
├── crm-synchronization/   # CRM sync components
├── calendar-management/   # Calendar management components
├── pricing/               # Pricing page components
├── about-us/              # About us page components
├── contact-us/            # Contact page components
├── blog/                  # Blog components
├── portfolio/             # Portfolio components
├── careers/               # Careers page components
├── styles/                # Global styles
└── public/                # Static assets
```

## Development

The application uses a component-based architecture with each page having its own directory containing an `index.jsx` file and a `components/` folder with reusable UI components.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## License

This project is licensed under the MIT License.
