"use client";

import React, { useState } from "react";
import { RxChevronDown } from "react-icons/rx";
import Link from "next/link";

// Simple Button component
const Button = ({ children, title, variant = "primary", size = "md", className = "", onClick, ...props }) => {
  const baseClasses = "inline-flex items-center justify-center font-medium transition-colors rounded-md";
  const variantClasses = {
    primary: "bg-blue-600 text-white hover:bg-blue-700",
    secondary: "bg-gray-200 text-gray-900 hover:bg-gray-300",
    link: "text-blue-600 hover:text-blue-800 bg-transparent p-0 underline"
  };
  const sizeClasses = {
    sm: "px-3 py-1.5 text-sm",
    md: "px-4 py-2 text-base",
    lg: "px-6 py-3 text-lg"
  };
  
  return (
    <button 
      className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`}
      onClick={onClick}
      {...props}
    >
      {title || children}
    </button>
  );
};

export function Navbar1() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  return (
    <nav className="w-full bg-white border-b border-gray-200 px-4 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center">
            <span className="text-xl font-bold text-gray-900">AI Dashboard</span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-8">
            <Link href="/home-page" className="text-gray-700 hover:text-blue-600">Home</Link>
            <Link href="/features-page" className="text-gray-700 hover:text-blue-600">Features</Link>
            <Link href="/pricing-page" className="text-gray-700 hover:text-blue-600">Pricing</Link>
            
            {/* Resources Dropdown */}
            <div className="relative">
              <button 
                className="flex items-center text-gray-700 hover:text-blue-600"
                onClick={() => setIsDropdownOpen(!isDropdownOpen)}
              >
                Resources <RxChevronDown className="ml-1" />
              </button>
              {isDropdownOpen && (
                <div className="absolute top-full left-0 mt-2 w-48 bg-white border border-gray-200 rounded-md shadow-lg z-50">
                  <Link href="/blog-page" className="block px-4 py-2 text-gray-700 hover:bg-gray-100">Blog</Link>
                  <Link href="/contact-us-page" className="block px-4 py-2 text-gray-700 hover:bg-gray-100">Contact Us</Link>
                  <Link href="/about-us-page" className="block px-4 py-2 text-gray-700 hover:bg-gray-100">About</Link>
                </div>
              )}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="hidden lg:flex items-center space-x-4">
            <Button variant="secondary" size="sm">Sign Up</Button>
            <Button size="sm">Login</Button>
          </div>

          {/* Mobile Menu Button */}
          <button 
            className="lg:hidden"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          >
            <span className="sr-only">Open menu</span>
            <div className="space-y-1">
              <div className="w-6 h-0.5 bg-gray-600"></div>
              <div className="w-6 h-0.5 bg-gray-600"></div>
              <div className="w-6 h-0.5 bg-gray-600"></div>
            </div>
          </button>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="lg:hidden py-4 border-t border-gray-200">
            <div className="space-y-4">
              <Link href="/home-page" className="block text-gray-700 hover:text-blue-600">Home</Link>
              <Link href="/features-page" className="block text-gray-700 hover:text-blue-600">Features</Link>
              <Link href="/pricing-page" className="block text-gray-700 hover:text-blue-600">Pricing</Link>
              <Link href="/blog-page" className="block text-gray-700 hover:text-blue-600">Blog</Link>
              <Link href="/contact-us-page" className="block text-gray-700 hover:text-blue-600">Contact</Link>
              <div className="flex space-x-4 pt-4">
                <Button variant="secondary" size="sm" className="w-full">Sign Up</Button>
                <Button size="sm" className="w-full">Login</Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
}
