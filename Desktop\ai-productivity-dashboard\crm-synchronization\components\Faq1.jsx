"use client";

import {
  Accordion,
  Accordion<PERSON>ontent,
  Accordion<PERSON><PERSON>,
  Accordion<PERSON>rigger,
  <PERSON>ton,
} from "../../components/ui";
import React from "react";

export function Faq1() {
  return (
    <section id="relume" className="px-[5%] py-16 md:py-24 lg:py-28">
      <div className="container max-w-lg">
        <div className="rb-12 mb-12 text-center md:mb-18 lg:mb-20">
          <h2 className="rb-5 mb-5 text-5xl font-bold md:mb-6 md:text-7xl lg:text-8xl">
            FAQs
          </h2>
          <p className="md:text-md">
            Here are some common questions regarding CRM synchronization and how
            it benefits your workflow.
          </p>
        </div>
        <Accordion type="multiple">
          <AccordionItem value="item-0">
            <AccordionTrigger className="md:py-5 md:text-md">
              What is CRM synchronization?
            </AccordionTrigger>
            <AccordionContent className="md:pb-6">
              CRM synchronization refers to the process of ensuring that all
              customer data is consistent across various platforms. This
              integration allows for seamless updates and access to customer
              information. It enhances communication and efficiency within your
              team.
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="item-1">
            <AccordionTrigger className="md:py-5 md:text-md">
              How does it work?
            </AccordionTrigger>
            <AccordionContent className="md:pb-6">
              Our system automatically syncs data between your CRM and other
              tools you use. This is achieved through real-time updates and API
              integrations. As a result, you can focus on your tasks without
              worrying about data discrepancies.
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="item-2">
            <AccordionTrigger className="md:py-5 md:text-md">
              Is it secure?
            </AccordionTrigger>
            <AccordionContent className="md:pb-6">
              Yes, we prioritize data security and compliance in our
              synchronization processes. Our system employs encryption and
              secure access protocols to protect your information. You can trust
              that your data is safe with us.
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="item-3">
            <AccordionTrigger className="md:py-5 md:text-md">
              Can I customize it?
            </AccordionTrigger>
            <AccordionContent className="md:pb-6">
              Absolutely! Our CRM synchronization can be tailored to meet your
              specific business needs. You can choose which data points to sync
              and how often updates occur, providing flexibility for your
              operations.
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="item-4">
            <AccordionTrigger className="md:py-5 md:text-md">
              What support is available?
            </AccordionTrigger>
            <AccordionContent className="md:pb-6">
              We offer comprehensive support for all users of our CRM
              synchronization feature. Our team is available to assist you with
              any questions or technical issues. You can reach out via our
              support channels for prompt assistance.
            </AccordionContent>
          </AccordionItem>
        </Accordion>
        <div className="mx-auto mt-12 max-w-md text-center md:mt-18 lg:mt-20">
          <h4 className="mb-3 text-2xl font-bold md:mb-4 md:text-3xl md:leading-[1.3] lg:text-4xl">
            Still have questions?
          </h4>
          <p className="md:text-md">
            We're here to help you with any inquiries.
          </p>
          <div className="mt-6 md:mt-8">
            <Button title="Contact" variant="secondary">
              Contact
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}
