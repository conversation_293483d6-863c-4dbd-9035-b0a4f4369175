import React from 'react'
import <PERSON> from 'next/link'
import Head from 'next/head'

export default function Home() {
  const featurePages = [
    {
      title: 'Home Page',
      description: 'Original home page design with real estate focus',
      href: '/home',
      icon: '🏠',
      color: 'blue'
    },
    {
      title: 'Task Management',
      description: 'Organize and prioritize your tasks with AI-powered insights',
      href: '/task-management',
      icon: '📋',
      color: 'blue'
    },
    {
      title: 'Email Automation',
      description: 'Automate your email workflows and responses',
      href: '/email-automation',
      icon: '📧',
      color: 'green'
    },
    {
      title: 'Document Generation',
      description: 'Generate professional documents with AI assistance',
      href: '/document-generation',
      icon: '📄',
      color: 'purple'
    },
    {
      title: 'Voice Processing',
      description: 'Convert speech to text and automate voice commands',
      href: '/voice-processing',
      icon: '🎤',
      color: 'yellow'
    },
    {
      title: 'CRM Synchronization',
      description: 'Sync and manage customer relationships seamlessly',
      href: '/crm-synchronization',
      icon: '🔄',
      color: 'red'
    },
    {
      title: 'Calendar Management',
      description: 'Smart scheduling and calendar optimization',
      href: '/calendar-management',
      icon: '📅',
      color: 'indigo'
    },
    {
      title: 'Notification System',
      description: 'Intelligent alerts and notification management',
      href: '/notification-system',
      icon: '🔔',
      color: 'pink'
    },
    {
      title: 'Features Overview',
      description: 'Comprehensive feature showcase and details',
      href: '/features-page',
      icon: '⭐',
      color: 'teal'
    },
    {
      title: 'Pricing Plans',
      description: 'Choose the perfect plan for your needs',
      href: '/pricing',
      icon: '💰',
      color: 'orange'
    },
    {
      title: 'Project Showcase',
      description: 'Individual project details and portfolio items',
      href: '/project',
      icon: '🚀',
      color: 'cyan'
    },
    {
      title: 'Blog Post',
      description: 'Individual blog post layout and content',
      href: '/blog-post',
      icon: '📰',
      color: 'lime'
    }
  ];

  const additionalPages = [
    { title: 'About Us', href: '/about-us', icon: '👥' },
    { title: 'Contact Us', href: '/contact-us', icon: '📞' },
    { title: 'Blog', href: '/blog', icon: '📝' },
    { title: 'Portfolio', href: '/portfolio', icon: '💼' },
    { title: 'Careers', href: '/careers', icon: '💼' },
    { title: 'Testimonials', href: '/testimonials', icon: '⭐' },
    { title: 'FAQ', href: '/faq', icon: '❓' }
  ];

  return (
    <>
      <Head>
        <title>AI Productivity Dashboard</title>
        <meta name="description" content="Revolutionize Your Workflow with AI Automation" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
        {/* Header */}
        <header className="bg-white shadow-sm">
          <div className="container mx-auto px-4 py-4">
            <nav className="flex justify-between items-center">
              <h1 className="text-2xl font-bold text-gray-900">AI Productivity Dashboard</h1>
              <div className="space-x-4">
                <Link href="/features-page" className="text-gray-600 hover:text-blue-600">Features</Link>
                <Link href="/pricing" className="text-gray-600 hover:text-blue-600">Pricing</Link>
                <Link href="/about-us" className="text-gray-600 hover:text-blue-600">About</Link>
                <Link href="/contact-us" className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">Contact</Link>
              </div>
            </nav>
          </div>
        </header>

        <div className="container mx-auto px-4 py-12">
          {/* Hero Section */}
          <div className="text-center mb-16">
            <h1 className="text-6xl font-bold mb-6 text-gray-900">
              AI Productivity Dashboard
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
              Revolutionize Your Workflow with AI Automation. Access all 19 available frontend pages including productivity tools, project showcases, and comprehensive business solutions.
            </p>
            <div className="flex justify-center space-x-4">
              <Link href="/features-page" className="bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 font-medium">
                Explore Features
              </Link>
              <Link href="/pricing" className="bg-white text-blue-600 px-8 py-3 rounded-lg border border-blue-600 hover:bg-blue-50 font-medium">
                View Pricing
              </Link>
            </div>
          </div>

          {/* Main Features Grid */}
          <div className="mb-16">
            <h2 className="text-3xl font-bold text-center mb-8 text-gray-900">All Available Pages</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {featurePages.map((feature) => (
                <div key={feature.href} className="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                  <div className="text-4xl mb-4">{feature.icon}</div>
                  <h3 className="text-xl font-semibold mb-3 text-gray-900">{feature.title}</h3>
                  <p className="text-gray-600 mb-4">{feature.description}</p>
                  <Link 
                    href={feature.href} 
                    className="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium"
                  >
                    Explore Feature
                    <svg className="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </Link>
                </div>
              ))}
            </div>
          </div>

          {/* Additional Pages */}
          <div className="mb-16">
            <h2 className="text-3xl font-bold text-center mb-8 text-gray-900">Additional Resources</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">
              {additionalPages.map((page) => (
                <Link
                  key={page.href}
                  href={page.href}
                  className="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow text-center group"
                >
                  <div className="text-2xl mb-2">{page.icon}</div>
                  <h3 className="text-sm font-medium text-gray-900 group-hover:text-blue-600">{page.title}</h3>
                </Link>
              ))}
            </div>
          </div>

          {/* Footer */}
          <footer className="text-center py-8 border-t border-gray-200">
            <p className="text-gray-600">
              © 2025 AI Productivity Dashboard. All rights reserved.
            </p>
          </footer>
        </div>
      </div>
    </>
  )
}
