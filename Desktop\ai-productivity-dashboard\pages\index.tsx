import React from 'react'
import <PERSON> from 'next/link'
import Head from 'next/head'

export default function Home() {
  // Core AI Productivity Features
  const coreFeatures = [
    {
      title: 'Task Management',
      description: 'Organize and prioritize your tasks with AI-powered insights',
      href: '/task-management',
      icon: '📋',
      category: 'productivity'
    },
    {
      title: 'Email Automation',
      description: 'Automate your email workflows and responses',
      href: '/email-automation',
      icon: '📧',
      category: 'automation'
    },
    {
      title: 'Document Generation',
      description: 'Generate professional documents with AI assistance',
      href: '/document-generation',
      icon: '📄',
      category: 'content'
    },
    {
      title: 'Voice Processing',
      description: 'Convert speech to text and automate voice commands',
      href: '/voice-processing',
      icon: '🎤',
      category: 'ai'
    },
    {
      title: 'CRM Synchronization',
      description: 'Sync and manage customer relationships seamlessly',
      href: '/crm-synchronization',
      icon: '🔄',
      category: 'integration'
    },
    {
      title: 'Calendar Management',
      description: 'Smart scheduling and calendar optimization',
      href: '/calendar-management',
      icon: '📅',
      category: 'productivity'
    },
    {
      title: 'Notification System',
      description: 'Intelligent alerts and notification management',
      href: '/notification-system',
      icon: '🔔',
      category: 'automation'
    },
    {
      title: 'Pricing Plans',
      description: 'Choose the perfect plan for your needs',
      href: '/pricing',
      icon: '💰',
      category: 'business'
    }
  ];

  // Alternative Pages & Showcases
  const showcasePages = [
    {
      title: 'Original Home Design',
      description: 'Alternative home page with real estate focus',
      href: '/home',
      icon: '🏠'
    },
    {
      title: 'Features Overview',
      description: 'Comprehensive feature showcase and details',
      href: '/features-page',
      icon: '⭐'
    },
    {
      title: 'Project Showcase',
      description: 'Individual project details and portfolio items',
      href: '/project',
      icon: '🚀'
    },
    {
      title: 'Blog Post Template',
      description: 'Individual blog post layout and content',
      href: '/blog-post',
      icon: '📰'
    }
  ];

  // Business & Content Pages
  const businessPages = [
    { title: 'About Us', href: '/about-us', icon: '👥', description: 'Learn about our company and team' },
    { title: 'Contact Us', href: '/contact-us', icon: '📞', description: 'Get in touch with our support team' },
    { title: 'Careers', href: '/careers', icon: '💼', description: 'Join our growing team' },
    { title: 'Testimonials', href: '/testimonials', icon: '⭐', description: 'See what our customers say' }
  ];

  // Content & Resources
  const contentPages = [
    { title: 'Blog', href: '/blog', icon: '📝', description: 'Latest articles and insights' },
    { title: 'Portfolio', href: '/portfolio', icon: '💼', description: 'Our work and projects' },
    { title: 'FAQ', href: '/faq', icon: '❓', description: 'Frequently asked questions' }
  ];

  // Developer & Admin Tools
  const developerTools = [
    { title: 'Error Analytics', href: '/error-analytics', icon: '📊', description: 'Monitor application errors and performance' },
    { title: 'Error Testing', href: '/error-test', icon: '🧪', description: 'Test error handling scenarios' }
  ];

  return (
    <>
      <Head>
        <title>AI Productivity Dashboard</title>
        <meta name="description" content="Revolutionize Your Workflow with AI Automation" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
        {/* Header */}
        <header className="bg-white shadow-sm">
          <div className="container mx-auto px-4 py-4">
            <nav className="flex justify-between items-center">
              <h1 className="text-2xl font-bold text-gray-900">AI Productivity Dashboard</h1>
              <div className="space-x-4">
                <Link href="/features-page" className="text-gray-600 hover:text-blue-600">Features</Link>
                <Link href="/pricing" className="text-gray-600 hover:text-blue-600">Pricing</Link>
                <Link href="/about-us" className="text-gray-600 hover:text-blue-600">About</Link>
                <Link href="/contact-us" className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">Contact</Link>
              </div>
            </nav>
          </div>
        </header>

        <div className="container mx-auto px-4 py-12">
          {/* Hero Section */}
          <div className="text-center mb-16">
            <h1 className="text-6xl font-bold mb-6 text-gray-900">
              AI Productivity Dashboard
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
              Revolutionize Your Workflow with AI Automation. Access all 26 available pages including core productivity tools, business solutions, content management, and developer utilities.
            </p>
            <div className="flex justify-center space-x-4">
              <Link href="/features-page" className="bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 font-medium">
                Explore Features
              </Link>
              <Link href="/pricing" className="bg-white text-blue-600 px-8 py-3 rounded-lg border border-blue-600 hover:bg-blue-50 font-medium">
                View Pricing
              </Link>
            </div>
          </div>

          {/* Core AI Features */}
          <div className="mb-16">
            <h2 className="text-3xl font-bold text-center mb-8 text-gray-900">🚀 Core AI Productivity Features</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {coreFeatures.map((feature) => (
                <div key={feature.href} className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                  <div className="text-3xl mb-3">{feature.icon}</div>
                  <h3 className="text-lg font-semibold mb-2 text-gray-900">{feature.title}</h3>
                  <p className="text-gray-600 text-sm mb-4">{feature.description}</p>
                  <Link
                    href={feature.href}
                    className="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium text-sm"
                  >
                    Explore
                    <svg className="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </Link>
                </div>
              ))}
            </div>
          </div>

          {/* Showcase Pages */}
          <div className="mb-16">
            <h2 className="text-3xl font-bold text-center mb-8 text-gray-900">🎨 Showcase & Templates</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {showcasePages.map((page) => (
                <div key={page.href} className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                  <div className="text-3xl mb-3">{page.icon}</div>
                  <h3 className="text-lg font-semibold mb-2 text-gray-900">{page.title}</h3>
                  <p className="text-gray-600 text-sm mb-4">{page.description}</p>
                  <Link
                    href={page.href}
                    className="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium text-sm"
                  >
                    View
                    <svg className="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </Link>
                </div>
              ))}
            </div>
          </div>

          {/* Business Pages */}
          <div className="mb-16">
            <h2 className="text-3xl font-bold text-center mb-8 text-gray-900">💼 Business & Company</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {businessPages.map((page) => (
                <div key={page.href} className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                  <div className="text-3xl mb-3">{page.icon}</div>
                  <h3 className="text-lg font-semibold mb-2 text-gray-900">{page.title}</h3>
                  <p className="text-gray-600 text-sm mb-4">{page.description}</p>
                  <Link
                    href={page.href}
                    className="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium text-sm"
                  >
                    Visit
                    <svg className="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </Link>
                </div>
              ))}
            </div>
          </div>

          {/* Content Pages */}
          <div className="mb-16">
            <h2 className="text-3xl font-bold text-center mb-8 text-gray-900">📝 Content & Resources</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {contentPages.map((page) => (
                <div key={page.href} className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                  <div className="text-3xl mb-3">{page.icon}</div>
                  <h3 className="text-lg font-semibold mb-2 text-gray-900">{page.title}</h3>
                  <p className="text-gray-600 text-sm mb-4">{page.description}</p>
                  <Link
                    href={page.href}
                    className="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium text-sm"
                  >
                    Browse
                    <svg className="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </Link>
                </div>
              ))}
            </div>
          </div>

          {/* Developer Tools */}
          <div className="mb-16">
            <h2 className="text-3xl font-bold text-center mb-8 text-gray-900">🛠️ Developer & Admin Tools</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {developerTools.map((tool) => (
                <div key={tool.href} className="bg-gray-50 p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border-2 border-dashed border-gray-300">
                  <div className="text-3xl mb-3">{tool.icon}</div>
                  <h3 className="text-lg font-semibold mb-2 text-gray-900">{tool.title}</h3>
                  <p className="text-gray-600 text-sm mb-4">{tool.description}</p>
                  <Link
                    href={tool.href}
                    className="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium text-sm"
                  >
                    Access
                    <svg className="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </Link>
                </div>
              ))}
            </div>
          </div>

          {/* Summary Stats */}
          <div className="bg-white p-8 rounded-xl shadow-lg text-center mb-8">
            <h3 className="text-2xl font-semibold mb-4 text-gray-900">📊 Complete Application Overview</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div>
                <div className="text-3xl font-bold text-blue-600">{coreFeatures.length}</div>
                <div className="text-sm text-gray-600">Core Features</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-green-600">{businessPages.length}</div>
                <div className="text-sm text-gray-600">Business Pages</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-purple-600">{contentPages.length}</div>
                <div className="text-sm text-gray-600">Content Pages</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-orange-600">26</div>
                <div className="text-sm text-gray-600">Total Pages</div>
              </div>
            </div>
            <p className="text-gray-600 mt-4">
              All pages are fully functional with comprehensive error handling, responsive design, and seamless navigation.
            </p>
          </div>

          {/* Footer */}
          <footer className="text-center py-8 border-t border-gray-200">
            <p className="text-gray-600">
              © 2025 AI Productivity Dashboard. All rights reserved.
            </p>
            <p className="text-sm text-gray-500 mt-2">
              Built with Next.js, TypeScript, and Tailwind CSS • All 26 pages connected and functional
            </p>
          </footer>
        </div>
      </div>
    </>
  )
}
