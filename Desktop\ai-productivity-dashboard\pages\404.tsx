import React, { useState, useEffect } from 'react';
import <PERSON> from 'next/link';
import Head from 'next/head';
import { Button } from '../components/ui';

export default function Custom404() {
  const [searchQuery, setSearchQuery] = useState('');
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const popularPages = [
    { title: 'Home', href: '/', icon: '🏠', description: 'Return to dashboard' },
    { title: 'Task Management', href: '/task-management', icon: '📋', description: 'Organize your tasks' },
    { title: 'Email Automation', href: '/email-automation', icon: '📧', description: 'Automate workflows' },
    { title: 'Document Generation', href: '/document-generation', icon: '📄', description: 'Create documents' },
    { title: 'Features', href: '/features-page', icon: '⭐', description: 'Explore all features' },
    { title: 'Pricing', href: '/pricing', icon: '💰', description: 'View pricing plans' },
    { title: 'About Us', href: '/about-us', icon: '👥', description: 'Learn about us' },
    { title: 'Contact', href: '/contact-us', icon: '📞', description: 'Get in touch' }
  ];

  const allPages = [
    { title: 'Home', href: '/', keywords: ['home', 'dashboard', 'main'] },
    { title: 'Task Management', href: '/task-management', keywords: ['task', 'todo', 'organize', 'management'] },
    { title: 'Email Automation', href: '/email-automation', keywords: ['email', 'automation', 'workflow', 'mail'] },
    { title: 'Document Generation', href: '/document-generation', keywords: ['document', 'generate', 'pdf', 'word'] },
    { title: 'Voice Processing', href: '/voice-processing', keywords: ['voice', 'speech', 'audio', 'processing'] },
    { title: 'CRM Synchronization', href: '/crm-synchronization', keywords: ['crm', 'sync', 'customer', 'relationship'] },
    { title: 'Calendar Management', href: '/calendar-management', keywords: ['calendar', 'schedule', 'appointment', 'time'] },
    { title: 'Notification System', href: '/notification-system', keywords: ['notification', 'alert', 'system', 'notify'] },
    { title: 'Features', href: '/features-page', keywords: ['features', 'overview', 'capabilities'] },
    { title: 'Pricing', href: '/pricing', keywords: ['pricing', 'plans', 'cost', 'subscription'] },
    { title: 'About Us', href: '/about-us', keywords: ['about', 'company', 'team', 'us'] },
    { title: 'Contact Us', href: '/contact-us', keywords: ['contact', 'support', 'help', 'reach'] },
    { title: 'Blog', href: '/blog', keywords: ['blog', 'articles', 'news', 'posts'] },
    { title: 'Portfolio', href: '/portfolio', keywords: ['portfolio', 'projects', 'work', 'showcase'] },
    { title: 'Careers', href: '/careers', keywords: ['careers', 'jobs', 'hiring', 'work'] },
    { title: 'Testimonials', href: '/testimonials', keywords: ['testimonials', 'reviews', 'feedback'] },
    { title: 'FAQ', href: '/faq', keywords: ['faq', 'questions', 'help', 'support'] }
  ];

  const filteredPages = searchQuery.trim() 
    ? allPages.filter(page => 
        page.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        page.keywords.some(keyword => keyword.toLowerCase().includes(searchQuery.toLowerCase()))
      )
    : [];

  if (!mounted) {
    return null; // Prevent hydration mismatch
  }

  return (
    <>
      <Head>
        <title>404 - Page Not Found | AI Productivity Dashboard</title>
        <meta name="description" content="The page you're looking for doesn't exist. Find what you need from our AI productivity tools and features." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>

      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
        {/* Header */}
        <header className="bg-white shadow-sm">
          <div className="container mx-auto px-4 py-4">
            <nav className="flex justify-between items-center">
              <Link href="/" className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">AI</span>
                </div>
                <h1 className="text-2xl font-bold text-gray-900">AI Productivity Dashboard</h1>
              </Link>
              <div className="space-x-4">
                <Link href="/features-page" className="text-gray-600 hover:text-blue-600">Features</Link>
                <Link href="/pricing" className="text-gray-600 hover:text-blue-600">Pricing</Link>
                <Link href="/about-us" className="text-gray-600 hover:text-blue-600">About</Link>
                <Link href="/contact-us" className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">Contact</Link>
              </div>
            </nav>
          </div>
        </header>

        <div className="container mx-auto px-4 py-12">
          {/* 404 Hero Section */}
          <div className="text-center mb-16">
            <div className="mb-8">
              <div className="text-9xl mb-4">🤖</div>
              <h1 className="text-6xl font-bold mb-6 text-gray-900">
                404
              </h1>
              <h2 className="text-3xl font-semibold mb-4 text-gray-700">
                Oops! Page Not Found
              </h2>
              <p className="text-xl text-gray-600 max-w-2xl mx-auto mb-8">
                The page you're looking for seems to have wandered off into the digital void. 
                But don't worry – our AI assistant is here to help you find what you need!
              </p>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row justify-center gap-4 mb-12">
              <Link href="/" className="bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 font-medium transition-colors">
                🏠 Back to Dashboard
              </Link>
              <Link href="/features-page" className="bg-white text-blue-600 px-8 py-3 rounded-lg border border-blue-600 hover:bg-blue-50 font-medium transition-colors">
                ⭐ Explore Features
              </Link>
              <Link href="/contact-us" className="bg-gray-100 text-gray-700 px-8 py-3 rounded-lg hover:bg-gray-200 font-medium transition-colors">
                📞 Get Help
              </Link>
            </div>
          </div>

          {/* Search Section */}
          <div className="max-w-2xl mx-auto mb-16">
            <div className="bg-white p-8 rounded-xl shadow-lg">
              <h3 className="text-2xl font-semibold mb-4 text-gray-900 text-center">
                🔍 Search for What You Need
              </h3>
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search for pages, features, or tools..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-lg"
                />
                <div className="absolute right-3 top-3 text-gray-400">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
              </div>
              
              {/* Search Results */}
              {searchQuery.trim() && (
                <div className="mt-4">
                  {filteredPages.length > 0 ? (
                    <div className="space-y-2">
                      <p className="text-sm text-gray-600 mb-3">Found {filteredPages.length} result(s):</p>
                      {filteredPages.map((page) => (
                        <Link
                          key={page.href}
                          href={page.href}
                          className="block p-3 bg-gray-50 rounded-lg hover:bg-blue-50 hover:text-blue-600 transition-colors"
                        >
                          <span className="font-medium">{page.title}</span>
                          <span className="text-gray-500 ml-2">({page.href})</span>
                        </Link>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-500 text-center py-4">
                      No pages found matching "{searchQuery}". Try a different search term.
                    </p>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Popular Pages */}
          <div className="mb-16">
            <h3 className="text-3xl font-bold text-center mb-8 text-gray-900">
              🌟 Popular Pages
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {popularPages.map((page) => (
                <Link
                  key={page.href}
                  href={page.href}
                  className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 group"
                >
                  <div className="text-3xl mb-3 group-hover:scale-110 transition-transform">{page.icon}</div>
                  <h4 className="text-lg font-semibold mb-2 text-gray-900 group-hover:text-blue-600">{page.title}</h4>
                  <p className="text-gray-600 text-sm">{page.description}</p>
                </Link>
              ))}
            </div>
          </div>

          {/* Help Section */}
          <div className="bg-white p-8 rounded-xl shadow-lg text-center">
            <h3 className="text-2xl font-semibold mb-4 text-gray-900">
              🤝 Still Need Help?
            </h3>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              If you can't find what you're looking for, our support team is here to help.
              We're committed to making your AI productivity experience seamless.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Link href="/contact-us" className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 font-medium transition-colors">
                📧 Contact Support
              </Link>
              <Link href="/faq" className="bg-gray-100 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-200 font-medium transition-colors">
                ❓ View FAQ
              </Link>
            </div>
          </div>

          {/* Footer */}
          <footer className="text-center py-8 mt-16 border-t border-gray-200">
            <p className="text-gray-600">
              © 2025 AI Productivity Dashboard. All rights reserved.
            </p>
          </footer>
        </div>
      </div>
    </>
  );
}
