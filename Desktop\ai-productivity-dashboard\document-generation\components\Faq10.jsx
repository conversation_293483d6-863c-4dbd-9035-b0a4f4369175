"use client";

import {
  Accordion,
  Accordion<PERSON>ontent,
  Accordion<PERSON><PERSON>,
  Accordion<PERSON><PERSON>ger,
  <PERSON><PERSON>,
} from "../../components/ui";
import React from "react";

export function Faq10() {
  return (
    <section id="relume" className="px-[5%] py-16 md:py-24 lg:py-28">
      <div className="container">
        <div className="mx-auto mb-12 w-full max-w-lg text-center md:mb-18 lg:mb-20">
          <h2 className="rb-5 mb-5 text-5xl font-bold md:mb-6 md:text-7xl lg:text-8xl">
            FAQs
          </h2>
          <p className="md:text-md">
            Explore our frequently asked questions about document generation to
            find the answers you need.
          </p>
        </div>
        <div className="grid w-full auto-rows-min grid-cols-1 items-start gap-x-12 md:grid-cols-2 lg:gap-x-16">
          <Accordion type="multiple" className="w-full">
            <AccordionItem
              value="item-faq10_accordion"
              className="overflow-hidden"
            >
              <AccordionTrigger className="md:py-5 md:text-md">
                What is document generation?
              </AccordionTrigger>
              <AccordionContent className="md:pb-6">
                Document generation is the process of creating digital documents
                automatically. Our system utilizes templates and data inputs to
                streamline this process. This ensures accuracy and saves
                valuable time.
              </AccordionContent>
            </AccordionItem>
            <AccordionItem
              value="item-faq10_accordion-2"
              className="overflow-hidden"
            >
              <AccordionTrigger className="md:py-5 md:text-md">
                How does it work?
              </AccordionTrigger>
              <AccordionContent className="md:pb-6">
                Our document generation tool integrates with your existing data
                sources. It pulls relevant information and populates templates
                in real-time. This automation reduces manual effort and enhances
                efficiency.
              </AccordionContent>
            </AccordionItem>
            <AccordionItem
              value="item-faq10_accordion-3"
              className="overflow-hidden"
            >
              <AccordionTrigger className="md:py-5 md:text-md">
                Can I customize templates?
              </AccordionTrigger>
              <AccordionContent className="md:pb-6">
                Yes, our platform allows you to customize document templates to
                fit your branding and requirements. You can modify layouts,
                colors, and content easily. This flexibility ensures your
                documents reflect your unique style.
              </AccordionContent>
            </AccordionItem>
            <AccordionItem
              value="item-faq10_accordion-4"
              className="overflow-hidden"
            >
              <AccordionTrigger className="md:py-5 md:text-md">
                Is it secure?
              </AccordionTrigger>
              <AccordionContent className="md:pb-6">
                Absolutely! We prioritize security and data protection in our
                document generation process. Our system employs encryption and
                complies with industry standards to safeguard your information.
              </AccordionContent>
            </AccordionItem>
            <AccordionItem
              value="item-faq10_accordion-5"
              className="overflow-hidden"
            >
              <AccordionTrigger className="md:py-5 md:text-md">
                What formats are supported?
              </AccordionTrigger>
              <AccordionContent className="md:pb-6">
                Our document generation tool supports various formats, including
                PDF, Word, and Excel. This versatility allows you to choose the
                best format for your needs. You can easily share or print
                documents in your preferred format.
              </AccordionContent>
            </AccordionItem>
          </Accordion>
          <Accordion type="multiple" className="w-full">
            <AccordionItem
              value="item-faq10_accordion-6"
              className="overflow-hidden first:border-t-0 md:first:border-t"
            >
              <AccordionTrigger className="md:py-5 md:text-md">
                Can I track changes?
              </AccordionTrigger>
              <AccordionContent className="md:pb-6">
                Yes, our system includes version control features that allow you
                to track changes made to documents. You can easily revert to
                previous versions if needed. This ensures you maintain a clear
                history of edits.
              </AccordionContent>
            </AccordionItem>
            <AccordionItem
              value="item-faq10_accordion-7"
              className="overflow-hidden first:border-t-0 md:first:border-t"
            >
              <AccordionTrigger className="md:py-5 md:text-md">
                Is training required?
              </AccordionTrigger>
              <AccordionContent className="md:pb-6">
                While our platform is user-friendly, we offer training resources
                to help you get started. These resources include tutorials and
                guides. Our support team is also available to assist you.
              </AccordionContent>
            </AccordionItem>
            <AccordionItem
              value="item-faq10_accordion-8"
              className="overflow-hidden first:border-t-0 md:first:border-t"
            >
              <AccordionTrigger className="md:py-5 md:text-md">
                Question text goes here
              </AccordionTrigger>
              <AccordionContent className="md:pb-6">
                Our system is designed to be intuitive, minimizing the need for
                extensive training. However, we encourage users to explore all
                features. This will help you maximize the benefits of our
                document generation tool.
              </AccordionContent>
            </AccordionItem>
            <AccordionItem
              value="item-faq10_accordion-9"
              className="overflow-hidden first:border-t-0 md:first:border-t"
            >
              <AccordionTrigger className="md:py-5 md:text-md">
                What support is available?
              </AccordionTrigger>
              <AccordionContent className="md:pb-6">
                We provide comprehensive support through various channels. You
                can access our help center, contact support via email, or chat
                with our team. We're here to ensure you have a seamless
                experience.
              </AccordionContent>
            </AccordionItem>
            <AccordionItem
              value="item-faq10_accordion-10"
              className="overflow-hidden first:border-t-0 md:first:border-t"
            >
              <AccordionTrigger className="md:py-5 md:text-md">
                How to get started?
              </AccordionTrigger>
              <AccordionContent className="md:pb-6">
                Getting started is easy! Simply sign up for an account, and you
                can begin exploring our document generation features. Our
                onboarding process will guide you through the initial setup.
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>
        <div className="mx-auto mt-12 max-w-md text-center md:mt-18 lg:mt-20">
          <h4 className="mb-3 text-2xl font-bold md:mb-4 md:text-3xl md:leading-[1.3] lg:text-4xl">
            Still have questions?
          </h4>
          <p className="md:text-md">
            Reach out to our support team for assistance.
          </p>
          <div className="mt-6 md:mt-8">
            <Button title="Contact" variant="secondary">
              Contact
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}
