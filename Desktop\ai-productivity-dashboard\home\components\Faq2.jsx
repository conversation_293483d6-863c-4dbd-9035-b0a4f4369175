"use client";

import {
  Accordion,
  Accordion<PERSON>ontent,
  Accordion<PERSON><PERSON>,
  Accordion<PERSON>rigger,
  <PERSON>ton,
} from "../../components/ui";
import React from "react";

export function Faq2() {
  return (
    <section id="relume" className="px-[5%] py-16 md:py-24 lg:py-28">
      <div className="container">
        <div className="rb-12 mb-12 w-full max-w-lg md:mb-18 lg:mb-20">
          <h2 className="rb-5 mb-5 text-5xl font-bold md:mb-6 md:text-7xl lg:text-8xl">
            FAQs
          </h2>
          <p className="md:text-md">
            Find answers to your most pressing questions about our AI Task
            Automation Assistant.
          </p>
        </div>
        <Accordion type="multiple">
          <AccordionItem value="item-0">
            <AccordionTrigger className="md:py-5 md:text-md">
              What is the system?
            </AccordionTrigger>
            <AccordionContent className="md:pb-6">
              Our system is an AI-powered productivity ecosystem designed to
              streamline tasks for real estate professionals. It integrates
              voice capture, task management, and email automation into one
              dashboard. This unified approach enhances efficiency and
              collaboration.
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="item-1">
            <AccordionTrigger className="md:py-5 md:text-md">
              How does it work?
            </AccordionTrigger>
            <AccordionContent className="md:pb-6">
              The system utilizes advanced AI technologies to process voice
              commands and automate tasks. It synchronizes with your calendar,
              CRM, and email to ensure seamless communication. This allows you
              to focus on what matters most—growing your business.
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="item-2">
            <AccordionTrigger className="md:py-5 md:text-md">
              Is it user-friendly?
            </AccordionTrigger>
            <AccordionContent className="md:pb-6">
              Absolutely! The intuitive design and user interface make it easy
              for anyone to navigate. Our system is built with real estate
              professionals in mind, ensuring a smooth user experience. You'll
              be up and running in no time!
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="item-3">
            <AccordionTrigger className="md:py-5 md:text-md">
              What support is available?
            </AccordionTrigger>
            <AccordionContent className="md:pb-6">
              We offer comprehensive support through various channels, including
              live chat, email, and phone. Our dedicated team is ready to assist
              you with any questions or concerns. You can also access a rich
              library of resources and tutorials.
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="item-4">
            <AccordionTrigger className="md:py-5 md:text-md">
              Can I customize it?
            </AccordionTrigger>
            <AccordionContent className="md:pb-6">
              Yes, our system is highly customizable to fit your specific
              business needs. You can tailor features and integrations to
              enhance your workflow. This flexibility ensures that you get the
              most out of our AI solutions.
            </AccordionContent>
          </AccordionItem>
        </Accordion>
        <div className="mt-12 md:mt-18 lg:mt-20">
          <h4 className="mb-3 text-2xl font-bold md:mb-4 md:text-3xl md:leading-[1.3] lg:text-4xl">
            Still have questions?
          </h4>
          <p className="md:text-md">We're here to help you!</p>
          <div className="mt-6 md:mt-8">
            <Button title="Contact" variant="secondary">
              Contact
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}
