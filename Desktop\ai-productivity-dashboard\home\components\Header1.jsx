"use client";

import React from "react";

// Simple Button component
const Button = ({ children, title, variant = "primary", className = "", onClick, ...props }) => {
  const baseClasses = "inline-flex items-center justify-center px-4 py-2 rounded-md font-medium transition-colors";
  const variantClasses = {
    primary: "bg-blue-600 text-white hover:bg-blue-700",
    secondary: "bg-gray-200 text-gray-900 hover:bg-gray-300",
    link: "text-blue-600 hover:text-blue-800 bg-transparent p-0 underline"
  };
  
  return (
    <button 
      className={`${baseClasses} ${variantClasses[variant]} ${className}`}
      onClick={onClick}
      {...props}
    >
      {title || children}
    </button>
  );
};

export function Header1() {
  return (
    <section id="relume" className="px-[5%] py-16 md:py-24 lg:py-28">
      <div className="container">
        <div className="grid grid-cols-1 gap-x-20 gap-y-12 md:gap-y-16 lg:grid-cols-2 lg:items-center">
          <div>
            <h1 className="mb-5 text-6xl font-bold md:mb-6 md:text-9xl lg:text-10xl">
              Revolutionize Your Workflow with AI Automation
            </h1>
            <p className="md:text-md">
              Unlock the potential of your business with our AI Task Automation
              Assistant. Streamline your tasks, enhance productivity, and focus
              on what truly matters.
            </p>
            <div className="mt-6 flex flex-wrap gap-4 md:mt-8">
              <Button title="Get Started">Get Started</Button>
              <Button title="Learn More" variant="secondary">
                Learn More
              </Button>
            </div>
          </div>
          <div>
            <img
              src="https://d22po4pjz3o32e.cloudfront.net/placeholder-image.svg"
              className="w-full rounded-lg object-cover"
              alt="AI Productivity Dashboard"
            />
          </div>
        </div>
      </div>
    </section>
  );
}
