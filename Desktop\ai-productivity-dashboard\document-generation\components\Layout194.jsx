"use client";

import React from "react";

export function Layout194() {
  return (
    <section id="relume" className="px-[5%] py-16 md:py-24 lg:py-28">
      <div className="container">
        <div className="grid grid-cols-1 items-center gap-12 md:grid-cols-2 lg:gap-x-20">
          <div className="order-2 md:order-1">
            <img
              src="https://d22po4pjz3o32e.cloudfront.net/placeholder-image.svg"
              className="w-full rounded-image object-cover"
              alt="Relume placeholder image"
            />
          </div>
          <div className="order-1 md:order-2">
            <h3 className="mb-5 text-4xl leading-[1.2] font-bold md:mb-6 md:text-5xl lg:text-6xl">
              Unlock Efficiency: Experience the Power of Automated Document
              Generation
            </h3>
            <p className="md:text-md">
              Automated document generation saves you valuable time by
              streamlining the creation process, allowing you to focus on what
              truly matters. Enjoy enhanced accuracy and consistency, reducing
              the risk of errors and ensuring your documents are always
              professional and reliable.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}
