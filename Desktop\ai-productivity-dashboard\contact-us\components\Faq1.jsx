"use client";

import {
  Accordion,
  Accordion<PERSON>ontent,
  Accordion<PERSON><PERSON>,
  Accordion<PERSON>rigger,
  <PERSON>ton,
} from "../../components/ui";
import React from "react";

export function Faq1() {
  return (
    <section id="relume" className="px-[5%] py-16 md:py-24 lg:py-28">
      <div className="container max-w-lg">
        <div className="rb-12 mb-12 text-center md:mb-18 lg:mb-20">
          <h2 className="rb-5 mb-5 text-5xl font-bold md:mb-6 md:text-7xl lg:text-8xl">
            FAQs
          </h2>
          <p className="md:text-md">
            Find quick answers to your most pressing questions about our
            services.
          </p>
        </div>
        <Accordion type="multiple">
          <AccordionItem value="item-0">
            <AccordionTrigger className="md:py-5 md:text-md">
              What is your service?
            </AccordionTrigger>
            <AccordionContent className="md:pb-6">
              Our service is an AI-powered productivity ecosystem designed for
              real estate professionals. It centralizes voice capture, task
              management, and email automation. This allows users to streamline
              their workflows and enhance productivity.
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="item-1">
            <AccordionTrigger className="md:py-5 md:text-md">
              How does it work?
            </AccordionTrigger>
            <AccordionContent className="md:pb-6">
              Our platform integrates various tools into a single dashboard.
              Users can capture voice notes, manage tasks, and automate emails
              seamlessly. The AI technology enhances efficiency and reduces
              manual effort.
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="item-2">
            <AccordionTrigger className="md:py-5 md:text-md">
              Is it user-friendly?
            </AccordionTrigger>
            <AccordionContent className="md:pb-6">
              Yes, our interface is designed with user experience in mind. It is
              intuitive and easy to navigate, ensuring that even those with
              minimal tech experience can use it effectively. We also provide
              tutorials and support for new users.
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="item-3">
            <AccordionTrigger className="md:py-5 md:text-md">
              What support do you offer?
            </AccordionTrigger>
            <AccordionContent className="md:pb-6">
              We offer comprehensive support through various channels, including
              email and live chat. Our team is available to assist with any
              questions or technical issues. Additionally, we provide a
              knowledge base for self-help.
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="item-4">
            <AccordionTrigger className="md:py-5 md:text-md">
              Can I try it?
            </AccordionTrigger>
            <AccordionContent className="md:pb-6">
              Absolutely! We offer a free trial for new users to explore our
              features. This allows you to experience the benefits without any
              commitment. Sign up today to get started!
            </AccordionContent>
          </AccordionItem>
        </Accordion>
        <div className="mx-auto mt-12 max-w-md text-center md:mt-18 lg:mt-20">
          <h4 className="mb-3 text-2xl font-bold md:mb-4 md:text-3xl md:leading-[1.3] lg:text-4xl">
            Still have questions?
          </h4>
          <p className="md:text-md">We're here to help you!</p>
          <div className="mt-6 md:mt-8">
            <Button title="Contact" variant="secondary">
              Contact
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}
