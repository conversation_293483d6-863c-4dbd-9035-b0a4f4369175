/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
    './home/<USER>/*.{js,ts,jsx,tsx}',
    './features/**/*.{js,ts,jsx,tsx}',
    './task-management/**/*.{js,ts,jsx,tsx}',
    './email-automation/**/*.{js,ts,jsx,tsx}',
    './document-generation/**/*.{js,ts,jsx,tsx}',
    './voice-processing/**/*.{js,ts,jsx,tsx}',
    './crm-synchronization/**/*.{js,ts,jsx,tsx}',
    './calendar-management/**/*.{js,ts,jsx,tsx}',
    './pricing/**/*.{js,ts,jsx,tsx}',
    './about-us/**/*.{js,ts,jsx,tsx}',
    './contact-us/**/*.{js,ts,jsx,tsx}',
    './blog/**/*.{js,ts,jsx,tsx}',
    './portfolio/**/*.{js,ts,jsx,tsx}',
    './careers/**/*.{js,ts,jsx,tsx}',
    './testimonials/**/*.{js,ts,jsx,tsx}',
    './notification-system/**/*.{js,ts,jsx,tsx}',
    './faq/**/*.{js,ts,jsx,tsx}',
    './project/**/*.{js,ts,jsx,tsx}',
    './blog-post/**/*.{js,ts,jsx,tsx}',
  ],
  theme: {
    extend: {
      spacing: {
        '18': '4.5rem',
      },
    },
  },
  plugins: [],
}
