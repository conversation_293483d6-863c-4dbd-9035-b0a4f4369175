import React from "react";

export function Footer10() {
  return (
    <footer className="bg-gray-900 text-white py-12">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <h3 className="text-lg font-bold mb-4">AI Dashboard</h3>
            <p className="text-gray-400">
              Revolutionizing productivity with AI automation for real estate professionals.
            </p>
          </div>
          
          <div>
            <h4 className="font-semibold mb-4">Features</h4>
            <ul className="space-y-2 text-gray-400">
              <li><a href="/task-management-page" className="hover:text-white">Task Management</a></li>
              <li><a href="/email-automation-page" className="hover:text-white">Email Automation</a></li>
              <li><a href="/document-generation-page" className="hover:text-white">Document Generation</a></li>
              <li><a href="/voice-processing-page" className="hover:text-white">Voice Processing</a></li>
            </ul>
          </div>
          
          <div>
            <h4 className="font-semibold mb-4">Company</h4>
            <ul className="space-y-2 text-gray-400">
              <li><a href="/about-us-page" className="hover:text-white">About Us</a></li>
              <li><a href="/careers-page" className="hover:text-white">Careers</a></li>
              <li><a href="/contact-us-page" className="hover:text-white">Contact</a></li>
              <li><a href="/blog-page" className="hover:text-white">Blog</a></li>
            </ul>
          </div>
          
          <div>
            <h4 className="font-semibold mb-4">Support</h4>
            <ul className="space-y-2 text-gray-400">
              <li><a href="/faq" className="hover:text-white">FAQ</a></li>
              <li><a href="/pricing-page" className="hover:text-white">Pricing</a></li>
              <li><a href="/contact-us-page" className="hover:text-white">Support</a></li>
            </ul>
          </div>
        </div>
        
        <div className="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
          <p>&copy; 2025 AI Productivity Dashboard. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
}
