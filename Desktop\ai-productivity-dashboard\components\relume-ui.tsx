import React from 'react';

// Mock Button component to replace @relume_io/relume-ui Button
export interface ButtonProps {
  children: React.ReactNode;
  title?: string;
  variant?: 'primary' | 'secondary' | 'link';
  size?: 'sm' | 'md' | 'lg' | 'link' | 'primary';
  iconRight?: React.ReactNode;
  iconLeft?: React.ReactNode;
  className?: string;
  onClick?: () => void;
  href?: string;
}

export function Button({ 
  children, 
  title, 
  variant = 'primary', 
  size = 'md', 
  iconRight, 
  iconLeft,
  className = '',
  onClick,
  href
}: ButtonProps) {
  const baseClasses = 'inline-flex items-center justify-center font-medium transition-colors rounded-md';
  
  const variantClasses = {
    primary: 'bg-blue-600 text-white hover:bg-blue-700',
    secondary: 'bg-gray-200 text-gray-900 hover:bg-gray-300',
    link: 'text-blue-600 hover:text-blue-800 bg-transparent p-0 underline'
  };
  
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg',
    link: 'p-0 text-base',
    primary: 'px-6 py-3 text-base'
  };
  
  const classes = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`;
  
  const content = (
    <>
      {iconLeft && <span className="mr-2">{iconLeft}</span>}
      {title || children}
      {iconRight && <span className="ml-2">{iconRight}</span>}
    </>
  );
  
  if (href) {
    return (
      <a href={href} className={classes}>
        {content}
      </a>
    );
  }
  
  return (
    <button className={classes} onClick={onClick}>
      {content}
    </button>
  );
}

// Mock Badge component
export interface BadgeProps {
  children: React.ReactNode;
  className?: string;
}

export function Badge({ children, className = '' }: BadgeProps) {
  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 ${className}`}>
      {children}
    </span>
  );
}

// Mock useMediaQuery hook
export function useMediaQuery(query: string): boolean {
  if (typeof window === 'undefined') return false;
  
  const [matches, setMatches] = React.useState(() => {
    return window.matchMedia(query).matches;
  });
  
  React.useEffect(() => {
    const mediaQuery = window.matchMedia(query);
    const handler = (event: MediaQueryListEvent) => setMatches(event.matches);
    
    mediaQuery.addEventListener('change', handler);
    return () => mediaQuery.removeEventListener('change', handler);
  }, [query]);
  
  return matches;
}

// Mock Input component
export interface InputProps {
  placeholder?: string;
  type?: string;
  className?: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

export function Input({ placeholder, type = 'text', className = '', value, onChange }: InputProps) {
  return (
    <input
      type={type}
      placeholder={placeholder}
      className={`px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${className}`}
      value={value}
      onChange={onChange}
    />
  );
}

// Mock Card component
export interface CardProps {
  children: React.ReactNode;
  className?: string;
}

export function Card({ children, className = '' }: CardProps) {
  return (
    <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
      {children}
    </div>
  );
}
