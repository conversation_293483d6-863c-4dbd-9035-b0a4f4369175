import React from 'react';
import dynamic from 'next/dynamic';
import Head from 'next/head';

const TaskManagementPage = dynamic(() => import('../task-management/index.jsx'), {
  ssr: false
});

export default function TaskManagement() {
  return (
    <>
      <Head>
        <title>Task Management - AI Productivity Dashboard</title>
        <meta name="description" content="Streamline your task management with AI automation" />
      </Head>
      <TaskManagementPage />
    </>
  );
}
