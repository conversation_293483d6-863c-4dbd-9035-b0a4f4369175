"use client";

import {
  Accordion,
  Accordion<PERSON>ontent,
  Accordion<PERSON><PERSON>,
  Accordion<PERSON>rigger,
  <PERSON><PERSON>,
} from "../../components/ui";
import React from "react";

export function Faq1() {
  return (
    <section id="relume" className="px-[5%] py-16 md:py-24 lg:py-28">
      <div className="container max-w-lg">
        <div className="rb-12 mb-12 text-center md:mb-18 lg:mb-20">
          <h2 className="rb-5 mb-5 text-5xl font-bold md:mb-6 md:text-7xl lg:text-8xl">
            FAQs
          </h2>
          <p className="md:text-md">
            Find answers to your questions about our Calendar Management feature
            below.
          </p>
        </div>
        <Accordion type="multiple">
          <AccordionItem value="item-0">
            <AccordionTrigger className="md:py-5 md:text-md">
              How does it work?
            </AccordionTrigger>
            <AccordionContent className="md:pb-6">
              Our Calendar Management feature integrates seamlessly with your
              existing calendars. It allows you to schedule, manage, and sync
              appointments effortlessly. You can also receive notifications for
              upcoming events.
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="item-1">
            <AccordionTrigger className="md:py-5 md:text-md">
              Can I sync calendars?
            </AccordionTrigger>
            <AccordionContent className="md:pb-6">
              Yes, you can sync multiple calendars from different platforms.
              This ensures that all your appointments are in one place. Simply
              connect your calendars through the settings.
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="item-2">
            <AccordionTrigger className="md:py-5 md:text-md">
              Is it user-friendly?
            </AccordionTrigger>
            <AccordionContent className="md:pb-6">
              Absolutely! The interface is designed for ease of use. You can
              navigate through features quickly and intuitively.
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="item-3">
            <AccordionTrigger className="md:py-5 md:text-md">
              What notifications are available?
            </AccordionTrigger>
            <AccordionContent className="md:pb-6">
              You can receive reminders for upcoming events via email or push
              notifications. Customizable settings allow you to choose how and
              when you want to be notified. Stay on top of your schedule
              effortlessly!
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="item-4">
            <AccordionTrigger className="md:py-5 md:text-md">
              Is there mobile access?
            </AccordionTrigger>
            <AccordionContent className="md:pb-6">
              Yes, our Calendar Management feature is accessible via a
              Progressive Web App. This means you can manage your calendar
              on-the-go from any device. Enjoy flexibility and convenience
              wherever you are.
            </AccordionContent>
          </AccordionItem>
        </Accordion>
        <div className="mx-auto mt-12 max-w-md text-center md:mt-18 lg:mt-20">
          <h4 className="mb-3 text-2xl font-bold md:mb-4 md:text-3xl md:leading-[1.3] lg:text-4xl">
            Still have questions?
          </h4>
          <p className="md:text-md">We're here to help you!</p>
          <div className="mt-6 md:mt-8">
            <Button title="Contact" variant="secondary">
              Contact
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}
