import React from "react";
import { Navbar1 } from "./components/Navbar1_simple";
import { Header1 } from "./components/Header1";
import { Layout245 } from "./components/Layout245_simple";
import { Footer10 } from "./components/Footer10_simple";

export default function Page() {
  return (
    <div>
      <Navbar1 />
      <Header1 />
      <Layout245 />
      <div className="text-center py-16 bg-gray-50">
        <h2 className="text-3xl font-bold mb-4">Transform Your Real Estate Business</h2>
        <p className="text-gray-600 max-w-2xl mx-auto mb-8">
          Join thousands of real estate professionals who have streamlined their workflows 
          with our AI-powered automation platform. Experience increased productivity and 
          focus on what truly matters - growing your business.
        </p>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
          <div className="text-center">
            <div className="text-4xl font-bold text-blue-600 mb-2">90%</div>
            <p className="text-gray-600">Time saved on routine tasks</p>
          </div>
          <div className="text-center">
            <div className="text-4xl font-bold text-green-600 mb-2">75%</div>
            <p className="text-gray-600">Faster email responses</p>
          </div>
          <div className="text-center">
            <div className="text-4xl font-bold text-purple-600 mb-2">50%</div>
            <p className="text-gray-600">Increase in productivity</p>
          </div>
        </div>
      </div>
      <Footer10 />
    </div>
  );
}
