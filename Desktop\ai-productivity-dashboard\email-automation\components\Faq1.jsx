"use client";

import {
  Accordion,
  Accordion<PERSON>ontent,
  Accordion<PERSON><PERSON>,
  Accordion<PERSON>rigger,
  <PERSON><PERSON>,
} from "../../components/ui";
import React from "react";

export function Faq1() {
  return (
    <section id="relume" className="px-[5%] py-16 md:py-24 lg:py-28">
      <div className="container max-w-lg">
        <div className="rb-12 mb-12 text-center md:mb-18 lg:mb-20">
          <h2 className="rb-5 mb-5 text-5xl font-bold md:mb-6 md:text-7xl lg:text-8xl">
            FAQs
          </h2>
          <p className="md:text-md">
            Discover how our email automation can streamline your communication
            and enhance productivity.
          </p>
        </div>
        <Accordion type="multiple">
          <AccordionItem value="item-0">
            <AccordionTrigger className="md:py-5 md:text-md">
              What is email automation?
            </AccordionTrigger>
            <AccordionContent className="md:pb-6">
              Email automation is a technology that allows you to send emails
              automatically based on specific triggers. It helps in managing
              communications efficiently, ensuring timely responses. This system
              can save you time and improve engagement with your audience.
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="item-1">
            <AccordionTrigger className="md:py-5 md:text-md">
              How does it work?
            </AccordionTrigger>
            <AccordionContent className="md:pb-6">
              Our email automation integrates seamlessly with your existing
              systems. It uses predefined rules to send messages based on user
              actions or schedules. This ensures that your communications are
              relevant and timely.
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="item-2">
            <AccordionTrigger className="md:py-5 md:text-md">
              Can I customize emails?
            </AccordionTrigger>
            <AccordionContent className="md:pb-6">
              Yes, you can fully customize your emails to match your brand's
              voice and style. Our platform allows you to create templates that
              can be reused for various campaigns. This flexibility enhances
              your communication strategy.
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="item-3">
            <AccordionTrigger className="md:py-5 md:text-md">
              Is it user-friendly?
            </AccordionTrigger>
            <AccordionContent className="md:pb-6">
              Absolutely! Our email automation system is designed with user
              experience in mind. You can easily navigate through the dashboard
              and set up your automation without any technical expertise.
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="item-4">
            <AccordionTrigger className="md:py-5 md:text-md">
              What support is available?
            </AccordionTrigger>
            <AccordionContent className="md:pb-6">
              We offer comprehensive support through various channels including
              email, chat, and phone. Our team is dedicated to helping you
              maximize the benefits of our email automation. You can also access
              a library of resources and tutorials.
            </AccordionContent>
          </AccordionItem>
        </Accordion>
        <div className="mx-auto mt-12 max-w-md text-center md:mt-18 lg:mt-20">
          <h4 className="mb-3 text-2xl font-bold md:mb-4 md:text-3xl md:leading-[1.3] lg:text-4xl">
            Still have questions?
          </h4>
          <p className="md:text-md">We're here to help you!</p>
          <div className="mt-6 md:mt-8">
            <Button title="Contact" variant="secondary">
              Contact
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}
