"use client";

import { Button } from "../../components/ui";
import React from "react";

export function Faq7() {
  return (
    <section id="relume" className="px-[5%] py-16 md:py-24 lg:py-28">
      <div className="container w-full max-w-lg">
        <div className="rb-12 mb-12 text-center md:mb-18 lg:mb-20">
          <h2 className="rb-5 mb-5 text-5xl font-bold md:mb-6 md:text-7xl lg:text-8xl">
            FAQs
          </h2>
          <p className="md:text-md">
            Find answers to common questions about our Task Management system
            and its features.
          </p>
        </div>
        <div className="grid grid-cols-1 gap-x-12 gap-y-10 md:gap-y-12">
          <div>
            <h2 className="mb-3 text-base font-bold md:mb-4 md:text-md">
              How does it work?
            </h2>
            <p>
              Our Task Management system integrates voice capture and task
              automation into a single dashboard. It allows users to efficiently
              manage tasks, set reminders, and track progress. This streamlines
              workflows and enhances productivity.
            </p>
          </div>
          <div>
            <h2 className="mb-3 text-base font-bold md:mb-4 md:text-md">
              Can I customize tasks?
            </h2>
            <p>
              Yes, you can customize tasks to fit your specific needs. The
              system allows for personalized task categories, deadlines, and
              priority settings. This ensures that you can tailor your workflow
              to your preferences.
            </p>
          </div>
          <div>
            <h2 className="mb-3 text-base font-bold md:mb-4 md:text-md">
              Is it mobile-friendly?
            </h2>
            <p>
              Absolutely! Our Task Management system is designed as a
              Progressive Web App (PWA). This means you can access it seamlessly
              on any device, ensuring productivity on the go.
            </p>
          </div>
          <div>
            <h2 className="mb-3 text-base font-bold md:mb-4 md:text-md">
              What support is available?
            </h2>
            <p>
              We offer comprehensive support through various channels including
              email and live chat. Our support team is available to assist you
              with any questions or issues. We also provide a detailed knowledge
              base for self-help.
            </p>
          </div>
          <div>
            <h2 className="mb-3 text-base font-bold md:mb-4 md:text-md">
              How secure is it?
            </h2>
            <p>
              Security is a top priority for us. We implement industry-standard
              encryption and data protection measures to safeguard your
              information. You can trust that your data is secure with our Task
              Management system.
            </p>
          </div>
        </div>
        <div className="mx-auto mt-12 max-w-md text-center md:mt-18 lg:mt-20">
          <h4 className="mb-3 text-2xl font-bold md:mb-4 md:text-3xl md:leading-[1.3] lg:text-4xl">
            Still have questions?
          </h4>
          <p className="md:text-md">We're here to help you!</p>
          <div className="mt-6 md:mt-8">
            <Button title="Contact" variant="secondary">
              Contact
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}
