import React from "react";

export function Layout245() {
  return (
    <section className="py-16 md:py-24 lg:py-28 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold mb-6 md:text-5xl lg:text-6xl">
            Key Features of Our AI Platform
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Discover the powerful features that make our AI productivity dashboard 
            the perfect solution for real estate professionals.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="text-center">
            <div className="mb-6">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto">
                <span className="text-2xl">📋</span>
              </div>
            </div>
            <h3 className="text-2xl font-bold mb-4">Efficient Task Management</h3>
            <p className="text-gray-600">Stay on top of your tasks with ease.</p>
          </div>
          
          <div className="text-center">
            <div className="mb-6">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                <span className="text-2xl">📧</span>
              </div>
            </div>
            <h3 className="text-2xl font-bold mb-4">Email Automation</h3>
            <p className="text-gray-600">Automate your emails and streamline communication effortlessly.</p>
          </div>
          
          <div className="text-center">
            <div className="mb-6">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto">
                <span className="text-2xl">📄</span>
              </div>
            </div>
            <h3 className="text-2xl font-bold mb-4">Document Generation</h3>
            <p className="text-gray-600">Generate professional documents automatically.</p>
          </div>
        </div>
      </div>
    </section>
  );
}
