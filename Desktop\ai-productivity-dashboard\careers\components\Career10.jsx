"use client";

import { <PERSON><PERSON>, <PERSON><PERSON> } from "../../components/ui";
import React from "react";
import { LuMapPin } from "react-icons/lu";
import { MdAccessTime } from "react-icons/md";

export function Career10() {
  return (
    <section id="relume" className="px-[5%] py-16 md:py-24 lg:py-28">
      <div className="container max-w-lg">
        <div className="mb-12 text-center md:mb-18 lg:mb-20">
          <p className="mb-3 font-semibold md:mb-4">Join</p>
          <h2 className="mb-5 text-5xl font-bold md:mb-6 md:text-7xl lg:text-8xl">
            Open Positions
          </h2>
          <p className="md:text-md">
            Explore exciting career opportunities and become part of our
            innovative team today!
          </p>
        </div>
        <div>
          <div className="border-t border-border-primary py-6 md:py-8">
            <div className="mb-3 flex items-center gap-4 md:mb-4">
              <h3 className="text-xl font-bold md:text-2xl">
                Software Engineer
              </h3>
              <Badge>Engineering</Badge>
            </div>
            <p className="mb-5 md:mb-6">
              Develop and maintain our core applications using cutting-edge
              technologies and frameworks.
            </p>
            <div className="flex flex-wrap gap-y-3">
              <div className="mr-6 flex items-center">
                <div className="mr-3 flex-none">
                  <LuMapPin className="flex size-6 flex-col items-center justify-center" />
                </div>
                <span className="md:text-md">Remote</span>
              </div>
              <div className="mr-6 flex items-center">
                <div className="mr-3 flex-none">
                  <MdAccessTime className="flex size-6 flex-col items-center justify-center" />
                </div>
                <span className="md:text-md">Full Time</span>
              </div>
            </div>
            <Button
              className="mt-6 md:mt-8"
              title="Apply Now"
              variant="secondary"
              size="sm"
            >
              <a href="#">Apply Now</a>
            </Button>
          </div>
          <div className="border-t border-border-primary py-6 md:py-8">
            <div className="mb-3 flex items-center gap-4 md:mb-4">
              <h3 className="text-xl font-bold md:text-2xl">Product Manager</h3>
              <Badge>Product</Badge>
            </div>
            <p className="mb-5 md:mb-6">
              Lead product strategy and drive the development of innovative
              solutions for our clients.
            </p>
            <div className="flex flex-wrap gap-y-3">
              <div className="mr-6 flex items-center">
                <div className="mr-3 flex-none">
                  <LuMapPin className="flex size-6 flex-col items-center justify-center" />
                </div>
                <span className="md:text-md">Hybrid</span>
              </div>
              <div className="mr-6 flex items-center">
                <div className="mr-3 flex-none">
                  <MdAccessTime className="flex size-6 flex-col items-center justify-center" />
                </div>
                <span className="md:text-md">Part Time</span>
              </div>
            </div>
            <Button
              className="mt-6 md:mt-8"
              title="Apply Now"
              variant="secondary"
              size="sm"
            >
              <a href="#">Apply Now</a>
            </Button>
          </div>
          <div className="border-t border-border-primary py-6 md:py-8">
            <div className="mb-3 flex items-center gap-4 md:mb-4">
              <h3 className="text-xl font-bold md:text-2xl">Data Analyst</h3>
              <Badge>Analytics</Badge>
            </div>
            <p className="mb-5 md:mb-6">
              Analyze data trends and provide insights to enhance our business
              strategies and operations.
            </p>
            <div className="flex flex-wrap gap-y-3">
              <div className="mr-6 flex items-center">
                <div className="mr-3 flex-none">
                  <LuMapPin className="flex size-6 flex-col items-center justify-center" />
                </div>
                <span className="md:text-md">Onsite</span>
              </div>
              <div className="mr-6 flex items-center">
                <div className="mr-3 flex-none">
                  <MdAccessTime className="flex size-6 flex-col items-center justify-center" />
                </div>
                <span className="md:text-md">Contract Role</span>
              </div>
            </div>
            <Button
              className="mt-6 md:mt-8"
              title="Apply Now"
              variant="secondary"
              size="sm"
            >
              <a href="#">Apply Now</a>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}
