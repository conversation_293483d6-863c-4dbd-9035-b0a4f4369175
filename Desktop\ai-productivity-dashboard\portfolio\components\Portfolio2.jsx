"use client";

import { <PERSON><PERSON>, <PERSON><PERSON> } from "../../components/ui";
import React from "react";

export function Portfolio2() {
  return (
    <section id="relume" className="px-[5%] py-16 md:py-24 lg:py-28">
      <div className="container">
        <div className="mb-12 md:mb-18 lg:mb-20">
          <div className="mx-auto max-w-lg text-center">
            <p className="mb-3 font-semibold md:mb-4">Portfolio</p>
            <h2 className="mb-5 text-5xl font-bold md:mb-6 md:text-7xl lg:text-8xl">
              Innovative Project Showcase
            </h2>
            <p className="md:text-md">
              Explore our cutting-edge automation solutions in action.
            </p>
          </div>
        </div>
        <div className="grid grid-cols-1 gap-12 md:gap-16 lg:gap-20">
          <div>
            <div>
              <a href="#">
                <img
                  src="https://d22po4pjz3o32e.cloudfront.net/placeholder-image-landscape.svg"
                  className="w-full rounded-image object-cover"
                  alt="Relume placeholder image"
                />
              </a>
            </div>
            <div className="mt-5 grid grid-cols-1 items-start justify-between gap-6 md:mt-6 md:grid-cols-2 md:gap-20">
              <div>
                <h3 className="text-xl font-bold md:text-2xl">
                  <a href="#">Real Estate Revolution</a>
                </h3>
                <div className="mt-3 flex flex-wrap gap-2 md:mt-4">
                  <Badge>
                    <a href="#">Real Estate</a>
                  </Badge>
                  <Badge>
                    <a href="#">Automation</a>
                  </Badge>
                  <Badge>
                    <a href="#">AI Solutions</a>
                  </Badge>
                </div>
              </div>
              <div>
                <p>
                  This project showcases how our AI Task Automation Assistant
                  transforms real estate workflows. Experience seamless
                  integration and enhanced productivity.
                </p>
              </div>
            </div>
          </div>
          <div>
            <div>
              <a href="#">
                <img
                  src="https://d22po4pjz3o32e.cloudfront.net/placeholder-image-landscape.svg"
                  className="w-full rounded-image object-cover"
                  alt="Relume placeholder image"
                />
              </a>
            </div>
            <div className="mt-5 grid grid-cols-1 items-start justify-between gap-6 md:mt-6 md:grid-cols-2 md:gap-20">
              <div>
                <h3 className="text-xl font-bold md:text-2xl">
                  <a href="#">Email Automation Excellence</a>
                </h3>
                <div className="mt-3 flex flex-wrap gap-2 md:mt-4">
                  <Badge>
                    <a href="#">Email Management</a>
                  </Badge>
                  <Badge>
                    <a href="#">Productivity Boost</a>
                  </Badge>
                  <Badge>
                    <a href="#">AI Integration</a>
                  </Badge>
                </div>
              </div>
              <div>
                <p>
                  Discover how our email automation system streamlines
                  communication. Save time and improve client engagement
                  effortlessly.
                </p>
              </div>
            </div>
          </div>
        </div>
        <div className="mt-12 flex justify-center md:mt-18 lg:mt-20">
          <Button title="View all" variant="secondary" size="primary">
            View all
          </Button>
        </div>
      </div>
    </section>
  );
}
