"use client";

import React from "react";

export function Layout90() {
  return (
    <section id="relume" className="px-[5%] py-16 md:py-24 lg:py-28">
      <div className="container">
        <div className="mb-12 grid grid-cols-1 items-start justify-between gap-x-12 gap-y-8 md:mb-18 md:grid-cols-2 md:gap-x-12 md:gap-y-8 lg:mb-20 lg:gap-x-20">
          <h3 className="text-4xl leading-[1.2] font-bold md:text-5xl lg:text-6xl">
            Effortlessly Track Tasks and Monitor Progress with Our Advanced
            Management Tools
          </h3>
          <p className="md:text-md">
            Our task management feature empowers you to stay organized and
            efficient. With real-time tracking and progress monitoring, you can
            easily oversee your projects from start to finish. Experience
            seamless collaboration and ensure nothing falls through the cracks.
          </p>
        </div>
        <img
          src="https://d22po4pjz3o32e.cloudfront.net/placeholder-image-landscape.svg"
          className="w-full rounded-image object-cover"
          alt="Relume placeholder image"
        />
      </div>
    </section>
  );
}
